/**
 * Debug script to test webhook configuration
 * Run with: node debug-webhook.js
 */

require('dotenv').config();

console.log('=== Webhook Configuration Debug ===\n');

// Check environment variables
const requiredEnvVars = [
  'STRIPE_SECRET_KEY',
  'STRIPE_WEBHOOK_SECRET',
  'STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID',
  'API_URL'
];

console.log('Environment Variables:');
requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // Mask sensitive values
    const maskedValue = varName.includes('SECRET') || varName.includes('KEY') 
      ? value.substring(0, 8) + '...' 
      : value;
    console.log(`✅ ${varName}: ${maskedValue}`);
  } else {
    console.log(`❌ ${varName}: NOT SET`);
  }
});

console.log('\n=== Webhook URL ===');
const apiUrl = process.env.API_URL;
if (apiUrl) {
  const webhookUrl = `${apiUrl}/stripe/webhook`;
  console.log(`Webhook URL: ${webhookUrl}`);
  console.log('Use this URL in Stripe Dashboard');
} else {
  console.log('❌ API_URL not set - cannot determine webhook URL');
}

console.log('\n=== Required Stripe Events ===');
const requiredEvents = [
  'checkout.session.completed',
  'customer.subscription.created', 
  'customer.subscription.updated',
  'customer.subscription.deleted',
  'invoice.payment_succeeded',
  'invoice.payment_failed'
];

console.log('Select these events in Stripe Dashboard:');
requiredEvents.forEach(event => {
  console.log(`- ${event}`);
});

console.log('\n=== API Version ===');
console.log('Use API version: 2023-10-16');

console.log('\n=== Test Commands ===');
console.log('1. Test with Stripe CLI:');
console.log(`   stripe listen --forward-to ${apiUrl}/stripe/webhook`);
console.log('\n2. Trigger test events:');
console.log('   stripe trigger customer.subscription.created');
console.log('   stripe trigger invoice.payment_succeeded');

console.log('\n=== Debug Steps ===');
console.log('1. Check CloudWatch logs:');
console.log('   aws logs tail /aws/lambda/gcandle-saas-api-prod-stripeWebhook --follow');
console.log('\n2. Test webhook endpoint:');
console.log(`   curl -X POST ${apiUrl}/stripe/webhook -H "Content-Type: application/json" -d '{}'`);
console.log('\n3. Verify in Stripe Dashboard:');
console.log('   Dashboard > Developers > Webhooks > [Your webhook] > Events');
